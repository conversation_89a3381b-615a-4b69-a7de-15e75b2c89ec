# Asset Flow Management

A modern asset flow management system built with Quarkus framework and JDK 24.

## Prerequisites

- JDK 24 or later
- Maven 3.9.0 or later

## Project Structure

```
asset-fow/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/assetfow/
│   │   │       └── GreetingResource.java
│   │   └── resources/
│   │       └── application.yml
│   └── test/
│       ├── java/
│       │   └── com/example/assetfow/
│       │       └── GreetingResourceTest.java
│       └── resources/
├── pom.xml
└── README.md
```

## Features

- **Quarkus Framework**: Fast startup and low memory footprint
- **JDK 24**: Latest Java features with preview features enabled
- **RESTEasy Reactive**: High-performance REST endpoints
- **Jackson**: JSON serialization/deserialization
- **OpenAPI/Swagger**: API documentation
- **Health Checks**: Application health monitoring
- **Metrics**: Application metrics with Micrometer
- **Configuration**: YAML-based configuration

## Getting Started

### Running the Application

1. **Development Mode** (with live reload):
   ```bash
   mvn quarkus:dev
   ```

2. **Package and Run**:
   ```bash
   mvn clean package
   java -jar target/quarkus-app/quarkus-run.jar
   ```

3. **Native Build** (requires GraalVM):
   ```bash
   mvn clean package -Pnative
   ./target/asset-fow-1.0.0-SNAPSHOT-runner
   ```

### Testing

Run all tests:
```bash
mvn test
```

Run integration tests:
```bash
mvn verify
```

### Accessing the Application

Once running, you can access:

- **Application**: http://localhost:8080
- **Hello Endpoint**: http://localhost:8080/hello
- **Health Check**: http://localhost:8080/q/health
- **Metrics**: http://localhost:8080/q/metrics
- **OpenAPI/Swagger UI**: http://localhost:8080/q/swagger-ui
- **Dev UI** (development mode only): http://localhost:8080/q/dev

## Configuration

The application configuration is located in `src/main/resources/application.yml`. Key configurations include:

- HTTP port and CORS settings
- Logging levels and formats
- Health check and metrics endpoints
- OpenAPI documentation settings

## Development

### Adding Dependencies

Use Maven to add new dependencies:
```bash
mvn quarkus:add-extension -Dextensions="extension-name"
```

### Available Quarkus Extensions

Some useful extensions you might want to add:
- `quarkus-hibernate-orm-panache` - Database ORM
- `quarkus-jdbc-postgresql` - PostgreSQL driver
- `quarkus-security-jpa` - Security with JPA
- `quarkus-scheduler` - Scheduled tasks
- `quarkus-cache` - Caching support

## Building for Production

1. **JVM Mode**:
   ```bash
   mvn clean package
   ```

2. **Native Mode** (smaller footprint, faster startup):
   ```bash
   mvn clean package -Pnative
   ```

## License

This project is licensed under the MIT License.
