# Asset Flow API Module

这是进销存系统的 API 模块，提供 REST 和 gRPC 接口。

## 功能特性

- **REST API**: 基于 JAX-RS 的 RESTful 接口
- **gRPC API**: 高性能的 gRPC 服务
- **OpenAPI/Swagger**: 自动生成的 API 文档
- **Bean Validation**: 请求参数验证
- **健康检查**: 提供健康检查端点

## API 端点

### REST API

#### 资产管理
- `GET /api/v1/assets` - 获取所有资产
- `GET /api/v1/assets/{id}` - 根据 ID 获取资产
- `POST /api/v1/assets` - 创建新资产
- `PUT /api/v1/assets/{id}` - 更新资产
- `DELETE /api/v1/assets/{id}` - 删除资产

#### 健康检查
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/health/ready` - 就绪检查
- `GET /api/v1/health/live` - 存活检查

### gRPC API

#### 服务端口
- gRPC 服务运行在端口 `9000`

#### 可用方法
- `GetAssets` - 获取资产列表（支持分页）
- `GetAssetById` - 根据 ID 获取资产
- `CreateAsset` - 创建新资产
- `UpdateAsset` - 更新资产
- `DeleteAsset` - 删除资产
- `StreamAssets` - 流式获取资产（实时更新）

## 运行应用

### 开发模式
```bash
cd asset-flow-api
mvn quarkus:dev
```

### 构建和运行
```bash
mvn clean package
java -jar target/quarkus-app/quarkus-run.jar
```

## 访问接口

### REST API
- **Swagger UI**: http://localhost:8080/q/swagger-ui
- **OpenAPI 规范**: http://localhost:8080/q/openapi
- **健康检查**: http://localhost:8080/api/v1/health

### gRPC
- **gRPC 端口**: localhost:9000
- **gRPC 反射**: 启用（可使用 grpcurl 等工具测试）

## 测试

### 运行所有测试
```bash
mvn test
```

### REST API 测试示例
```bash
# 获取所有资产
curl http://localhost:8080/api/v1/assets

# 创建资产
curl -X POST http://localhost:8080/api/v1/assets \
  -H "Content-Type: application/json" \
  -d '{"name":"测试资产","description":"测试描述","category":"设备","value":1000.0}'
```

### gRPC 测试示例（使用 grpcurl）
```bash
# 列出可用服务
grpcurl -plaintext localhost:9000 list

# 获取资产
grpcurl -plaintext -d '{"page":0,"size":10}' \
  localhost:9000 com.benlai.assetflow.api.grpc.AssetService/GetAssets
```

## 配置

主要配置在 `src/main/resources/application.yml` 中：

- HTTP 端口: 8080
- gRPC 端口: 9000
- CORS: 已启用
- OpenAPI: 已启用
- 日志级别: INFO（API 包为 DEBUG）

## 开发指南

### 添加新的 REST 端点
1. 在 `com.benlai.assetflow.api.rest` 包中创建新的资源类
2. 使用 JAX-RS 注解定义端点
3. 添加 OpenAPI 注解用于文档生成

### 添加新的 gRPC 服务
1. 在 `src/main/proto` 中定义 .proto 文件
2. 在 `com.benlai.assetflow.api.grpc` 包中实现服务
3. 使用 `@GrpcService` 注解标记服务类

### 测试
- REST API 测试使用 REST Assured
- gRPC 测试使用 Quarkus gRPC 测试支持
- 所有测试都使用 `@QuarkusTest` 注解
