package com.benlai.assetflow.api;

import io.quarkus.runtime.Quarkus;
import io.quarkus.runtime.QuarkusApplication;
import io.quarkus.runtime.annotations.QuarkusMain;

@QuarkusMain
public class AssetFlowApiApplication implements QuarkusApplication {

    public static void main(String... args) {
        Quarkus.run(AssetFlowApiApplication.class, args);
    }

    @Override
    public int run(String... args) throws Exception {
        System.out.println("Asset Flow API is starting...");
        Quarkus.waitForExit();
        return 0;
    }
}
