package com.benlai.assetflow.api.grpc;

import io.quarkus.grpc.GrpcService;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.Multi;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.UUID;

@GrpcService
public class AssetGrpcService implements AssetService {

    private static final Logger logger = LoggerFactory.getLogger(AssetGrpcService.class);

    @Override
    public Uni<GetAssetsResponse> getAssets(GetAssetsRequest request) {
        logger.info("Getting assets - page: {}, size: {}", request.getPage(), request.getSize());
        
        // TODO: Implement actual asset retrieval logic
        Asset sampleAsset = Asset.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setName("Sample Asset")
                .setDescription("This is a sample asset")
                .setCategory(request.getCategory().isEmpty() ? "general" : request.getCategory())
                .setValue(1000.0)
                .setCreatedAt(Instant.now().toEpochMilli())
                .setUpdatedAt(Instant.now().toEpochMilli())
                .setStatus("active")
                .build();

        GetAssetsResponse response = GetAssetsResponse.newBuilder()
                .addAssets(sampleAsset)
                .setTotalCount(1)
                .setPage(request.getPage())
                .setSize(request.getSize())
                .build();

        return Uni.createFrom().item(response);
    }

    @Override
    public Uni<AssetResponse> getAssetById(GetAssetByIdRequest request) {
        logger.info("Getting asset by ID: {}", request.getId());
        
        // TODO: Implement actual asset retrieval by ID logic
        Asset asset = Asset.newBuilder()
                .setId(request.getId())
                .setName("Asset " + request.getId())
                .setDescription("Asset with ID " + request.getId())
                .setCategory("general")
                .setValue(1500.0)
                .setCreatedAt(Instant.now().toEpochMilli())
                .setUpdatedAt(Instant.now().toEpochMilli())
                .setStatus("active")
                .build();

        AssetResponse response = AssetResponse.newBuilder()
                .setAsset(asset)
                .setMessage("Asset retrieved successfully")
                .setSuccess(true)
                .build();

        return Uni.createFrom().item(response);
    }

    @Override
    public Uni<AssetResponse> createAsset(CreateAssetRequest request) {
        logger.info("Creating asset: {}", request.getName());
        
        // TODO: Implement actual asset creation logic
        Asset asset = Asset.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setName(request.getName())
                .setDescription(request.getDescription())
                .setCategory(request.getCategory())
                .setValue(request.getValue())
                .setCreatedAt(Instant.now().toEpochMilli())
                .setUpdatedAt(Instant.now().toEpochMilli())
                .setStatus("active")
                .build();

        AssetResponse response = AssetResponse.newBuilder()
                .setAsset(asset)
                .setMessage("Asset created successfully")
                .setSuccess(true)
                .build();

        return Uni.createFrom().item(response);
    }

    @Override
    public Uni<AssetResponse> updateAsset(UpdateAssetRequest request) {
        logger.info("Updating asset: {}", request.getId());
        
        // TODO: Implement actual asset update logic
        Asset asset = Asset.newBuilder()
                .setId(request.getId())
                .setName(request.getName())
                .setDescription(request.getDescription())
                .setCategory(request.getCategory())
                .setValue(request.getValue())
                .setCreatedAt(Instant.now().minusSeconds(3600).toEpochMilli()) // 1 hour ago
                .setUpdatedAt(Instant.now().toEpochMilli())
                .setStatus(request.getStatus())
                .build();

        AssetResponse response = AssetResponse.newBuilder()
                .setAsset(asset)
                .setMessage("Asset updated successfully")
                .setSuccess(true)
                .build();

        return Uni.createFrom().item(response);
    }

    @Override
    public Uni<DeleteAssetResponse> deleteAsset(DeleteAssetRequest request) {
        logger.info("Deleting asset: {}", request.getId());
        
        // TODO: Implement actual asset deletion logic
        DeleteAssetResponse response = DeleteAssetResponse.newBuilder()
                .setMessage("Asset deleted successfully")
                .setSuccess(true)
                .build();

        return Uni.createFrom().item(response);
    }

    @Override
    public Multi<AssetResponse> streamAssets(StreamAssetsRequest request) {
        logger.info("Streaming assets for category: {}, status: {}", 
                   request.getCategory(), request.getStatus());
        
        // TODO: Implement actual asset streaming logic
        // This is a simple example that emits a few sample assets
        return Multi.createFrom().items(
                createSampleAssetResponse("1", "Streamed Asset 1"),
                createSampleAssetResponse("2", "Streamed Asset 2"),
                createSampleAssetResponse("3", "Streamed Asset 3")
        );
    }

    private AssetResponse createSampleAssetResponse(String id, String name) {
        Asset asset = Asset.newBuilder()
                .setId(id)
                .setName(name)
                .setDescription("Sample streamed asset")
                .setCategory("streaming")
                .setValue(Math.random() * 1000)
                .setCreatedAt(Instant.now().toEpochMilli())
                .setUpdatedAt(Instant.now().toEpochMilli())
                .setStatus("active")
                .build();

        return AssetResponse.newBuilder()
                .setAsset(asset)
                .setMessage("Asset streamed")
                .setSuccess(true)
                .build();
    }
}
