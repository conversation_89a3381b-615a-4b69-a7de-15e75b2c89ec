package com.benlai.assetflow.api.rest;

import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.Map;

@Path("/api/v1/assets")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class AssetResource {

    @GET
    public Response getAllAssets(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        
        // TODO: Implement asset retrieval logic
        return Response.ok(Map.of(
                "message", "Assets retrieved successfully",
                "page", page,
                "size", size,
                "data", List.of()
        )).build();
    }

    @GET
    @Path("/{id}")
    public Response getAssetById(@PathParam("id") @NotNull String id) {
        
        // TODO: Implement asset retrieval by ID logic
        return Response.ok(Map.of(
                "message", "Asset retrieved successfully",
                "id", id,
                "data", Map.of("id", id, "name", "Sample Asset")
        )).build();
    }

    @POST
    public Response createAsset(@Valid AssetRequest request) {
        
        // TODO: Implement asset creation logic
        return Response.status(Response.Status.CREATED)
                .entity(Map.of(
                        "message", "Asset created successfully",
                        "data", request
                )).build();
    }

    @PUT
    @Path("/{id}")
    public Response updateAsset(
            @PathParam("id") @NotNull String id,
            @Valid AssetRequest request) {
        
        // TODO: Implement asset update logic
        return Response.ok(Map.of(
                "message", "Asset updated successfully",
                "id", id,
                "data", request
        )).build();
    }

    @DELETE
    @Path("/{id}")
    public Response deleteAsset(@PathParam("id") @NotNull String id) {
        
        // TODO: Implement asset deletion logic
        return Response.noContent().build();
    }

    // Inner class for request body
    public static class AssetRequest {
        public String name;
        public String description;
        public String category;
        public Double value;
        
        // Constructors, getters, setters would be here
        public AssetRequest() {}
        
        public AssetRequest(String name, String description, String category, Double value) {
            this.name = name;
            this.description = description;
            this.category = category;
            this.value = value;
        }
    }
}
