package com.benlai.assetflow.api.rest;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.Map;

@Path("/api/v1/assets")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class AssetResource {

    @GET
    @Operation(summary = "Get all assets", description = "Retrieve a list of all assets")
    @ApiResponse(responseCode = "200", description = "Assets retrieved successfully",
            content = @Content(mediaType = "application/json"))
    public Response getAllAssets(
            @Parameter(description = "Page number") @QueryParam("page") @DefaultValue("0") int page,
            @Parameter(description = "Page size") @QueryParam("size") @DefaultValue("20") int size) {
        
        // TODO: Implement asset retrieval logic
        return Response.ok(Map.of(
                "message", "Assets retrieved successfully",
                "page", page,
                "size", size,
                "data", List.of()
        )).build();
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "Get asset by ID", description = "Retrieve a specific asset by its ID")
    @ApiResponse(responseCode = "200", description = "Asset found",
            content = @Content(mediaType = "application/json"))
    @ApiResponse(responseCode = "404", description = "Asset not found")
    public Response getAssetById(
            @Parameter(description = "Asset ID") @PathParam("id") @NotNull String id) {
        
        // TODO: Implement asset retrieval by ID logic
        return Response.ok(Map.of(
                "message", "Asset retrieved successfully",
                "id", id,
                "data", Map.of("id", id, "name", "Sample Asset")
        )).build();
    }

    @POST
    @Operation(summary = "Create new asset", description = "Create a new asset")
    @ApiResponse(responseCode = "201", description = "Asset created successfully",
            content = @Content(mediaType = "application/json"))
    @ApiResponse(responseCode = "400", description = "Invalid input")
    public Response createAsset(@Valid AssetRequest request) {
        
        // TODO: Implement asset creation logic
        return Response.status(Response.Status.CREATED)
                .entity(Map.of(
                        "message", "Asset created successfully",
                        "data", request
                )).build();
    }

    @PUT
    @Path("/{id}")
    @Operation(summary = "Update asset", description = "Update an existing asset")
    @ApiResponse(responseCode = "200", description = "Asset updated successfully")
    @ApiResponse(responseCode = "404", description = "Asset not found")
    public Response updateAsset(
            @Parameter(description = "Asset ID") @PathParam("id") @NotNull String id,
            @Valid AssetRequest request) {
        
        // TODO: Implement asset update logic
        return Response.ok(Map.of(
                "message", "Asset updated successfully",
                "id", id,
                "data", request
        )).build();
    }

    @DELETE
    @Path("/{id}")
    @Operation(summary = "Delete asset", description = "Delete an asset by ID")
    @ApiResponse(responseCode = "204", description = "Asset deleted successfully")
    @ApiResponse(responseCode = "404", description = "Asset not found")
    public Response deleteAsset(
            @Parameter(description = "Asset ID") @PathParam("id") @NotNull String id) {
        
        // TODO: Implement asset deletion logic
        return Response.noContent().build();
    }

    // Inner class for request body
    public static class AssetRequest {
        public String name;
        public String description;
        public String category;
        public Double value;
        
        // Constructors, getters, setters would be here
        public AssetRequest() {}
        
        public AssetRequest(String name, String description, String category, Double value) {
            this.name = name;
            this.description = description;
            this.category = category;
            this.value = value;
        }
    }
}
