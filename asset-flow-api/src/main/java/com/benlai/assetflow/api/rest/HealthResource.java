package com.benlai.assetflow.api.rest;

import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.time.Instant;
import java.util.Map;

@Path("/api/v1/health")
@Tag(name = "Health Check", description = "API health check operations")
@Produces(MediaType.APPLICATION_JSON)
public class HealthResource {

    @GET
    @Operation(summary = "Health check", description = "Check if the API is running")
    public Response health() {
        return Response.ok(Map.of(
                "status", "UP",
                "timestamp", Instant.now().toString(),
                "service", "asset-flow-api",
                "version", "1.0.0-SNAPSHOT",
                "features", Map.of(
                        "rest", "enabled",
                        "grpc", "enabled",
                        "openapi", "enabled"
                )
        )).build();
    }

    @GET
    @Path("/ready")
    @Operation(summary = "Readiness check", description = "Check if the API is ready to serve requests")
    public Response ready() {
        // TODO: Add actual readiness checks (database connectivity, etc.)
        return Response.ok(Map.of(
                "status", "READY",
                "timestamp", Instant.now().toString(),
                "checks", Map.of(
                        "database", "UP",
                        "grpc_server", "UP"
                )
        )).build();
    }

    @GET
    @Path("/live")
    @Operation(summary = "Liveness check", description = "Check if the API is alive")
    public Response live() {
        return Response.ok(Map.of(
                "status", "ALIVE",
                "timestamp", Instant.now().toString()
        )).build();
    }
}
