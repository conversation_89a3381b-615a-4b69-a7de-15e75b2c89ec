syntax = "proto3";

package com.benlai.assetflow.api.grpc;

option java_multiple_files = true;
option java_package = "com.benlai.assetflow.api.grpc";
option java_outer_classname = "AssetServiceProto";

// Asset Service Definition
service AssetService {
  // Get all assets with pagination
  rpc GetAssets(GetAssetsRequest) returns (GetAssetsResponse);
  
  // Get asset by ID
  rpc GetAssetById(GetAssetByIdRequest) returns (AssetResponse);
  
  // Create new asset
  rpc CreateAsset(CreateAssetRequest) returns (AssetResponse);
  
  // Update existing asset
  rpc UpdateAsset(UpdateAssetRequest) returns (AssetResponse);
  
  // Delete asset
  rpc DeleteAsset(DeleteAssetRequest) returns (DeleteAssetResponse);
  
  // Stream assets (for real-time updates)
  rpc StreamAssets(StreamAssetsRequest) returns (stream AssetResponse);
}

// Asset Message
message Asset {
  string id = 1;
  string name = 2;
  string description = 3;
  string category = 4;
  double value = 5;
  int64 created_at = 6;
  int64 updated_at = 7;
  string status = 8;
}

// Request Messages
message GetAssetsRequest {
  int32 page = 1;
  int32 size = 2;
  string category = 3;
  string status = 4;
}

message GetAssetByIdRequest {
  string id = 1;
}

message CreateAssetRequest {
  string name = 1;
  string description = 2;
  string category = 3;
  double value = 4;
}

message UpdateAssetRequest {
  string id = 1;
  string name = 2;
  string description = 3;
  string category = 4;
  double value = 5;
  string status = 6;
}

message DeleteAssetRequest {
  string id = 1;
}

message StreamAssetsRequest {
  string category = 1;
  string status = 2;
}

// Response Messages
message GetAssetsResponse {
  repeated Asset assets = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 size = 4;
}

message AssetResponse {
  Asset asset = 1;
  string message = 2;
  bool success = 3;
}

message DeleteAssetResponse {
  string message = 1;
  bool success = 2;
}
