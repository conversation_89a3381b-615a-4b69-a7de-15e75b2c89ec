# Asset Flow API Configuration
quarkus:
  application:
    name: asset-flow-api
    version: 1.0.0-SNAPSHOT
  
  # HTTP Configuration
  http:
    port: 8080
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      methods: "GET,PUT,POST,DELETE,OPTIONS"
      headers: "accept, authorization, content-type, x-requested-with"
  
  # gRPC Configuration - Temporarily disabled
  # grpc:
  #   server:
  #     port: 9000
  #     host: 0.0.0.0
  #   clients:
  #     # Configure gRPC clients here if needed
  
  # Logging Configuration
  log:
    level: INFO
    category:
      "com.benlai.assetflow.api": DEBUG
      "io.grpc": INFO
    console:
      enable: true
      format: "%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n"
  


# Custom application properties
api:
  version: v1
  base-path: /api/v1
  grpc:
    enabled: true
    reflection: true
