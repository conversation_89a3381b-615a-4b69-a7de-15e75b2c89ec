package com.benlai.assetflow.api.grpc;

import io.quarkus.grpc.GrpcClient;
import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.Uni;
import org.junit.jupiter.api.Test;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

@QuarkusTest
public class AssetGrpcServiceTest {

    @GrpcClient
    AssetService assetService;

    @Test
    public void testGetAssets() {
        GetAssetsRequest request = GetAssetsRequest.newBuilder()
                .setPage(0)
                .setSize(10)
                .setCategory("test")
                .build();

        Uni<GetAssetsResponse> response = assetService.getAssets(request);
        
        GetAssetsResponse result = response.await().atMost(Duration.ofSeconds(5));
        
        assertNotNull(result);
        assertEquals(0, result.getPage());
        assertEquals(10, result.getSize());
        assertTrue(result.getAssetsCount() >= 0);
    }

    @Test
    public void testGetAssetById() {
        GetAssetByIdRequest request = GetAssetByIdRequest.newBuilder()
                .setId("test-id")
                .build();

        Uni<AssetResponse> response = assetService.getAssetById(request);
        
        AssetResponse result = response.await().atMost(Duration.ofSeconds(5));
        
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("Asset retrieved successfully", result.getMessage());
        assertEquals("test-id", result.getAsset().getId());
    }

    @Test
    public void testCreateAsset() {
        CreateAssetRequest request = CreateAssetRequest.newBuilder()
                .setName("Test Asset")
                .setDescription("Test Description")
                .setCategory("test")
                .setValue(100.0)
                .build();

        Uni<AssetResponse> response = assetService.createAsset(request);
        
        AssetResponse result = response.await().atMost(Duration.ofSeconds(5));
        
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("Asset created successfully", result.getMessage());
        assertEquals("Test Asset", result.getAsset().getName());
        assertEquals(100.0, result.getAsset().getValue());
    }

    @Test
    public void testUpdateAsset() {
        UpdateAssetRequest request = UpdateAssetRequest.newBuilder()
                .setId("test-id")
                .setName("Updated Asset")
                .setDescription("Updated Description")
                .setCategory("test")
                .setValue(200.0)
                .setStatus("active")
                .build();

        Uni<AssetResponse> response = assetService.updateAsset(request);
        
        AssetResponse result = response.await().atMost(Duration.ofSeconds(5));
        
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("Asset updated successfully", result.getMessage());
        assertEquals("Updated Asset", result.getAsset().getName());
        assertEquals(200.0, result.getAsset().getValue());
    }

    @Test
    public void testDeleteAsset() {
        DeleteAssetRequest request = DeleteAssetRequest.newBuilder()
                .setId("test-id")
                .build();

        Uni<DeleteAssetResponse> response = assetService.deleteAsset(request);
        
        DeleteAssetResponse result = response.await().atMost(Duration.ofSeconds(5));
        
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("Asset deleted successfully", result.getMessage());
    }
}
