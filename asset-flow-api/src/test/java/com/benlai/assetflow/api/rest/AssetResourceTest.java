package com.benlai.assetflow.api.rest;

import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;

@QuarkusTest
public class AssetResourceTest {

    @Test
    public void testGetAllAssets() {
        given()
          .when().get("/api/v1/assets")
          .then()
             .statusCode(200)
             .body("message", is("Assets retrieved successfully"))
             .body("page", is(0))
             .body("size", is(20));
    }

    @Test
    public void testGetAssetById() {
        given()
          .when().get("/api/v1/assets/test-id")
          .then()
             .statusCode(200)
             .body("message", is("Asset retrieved successfully"))
             .body("id", is("test-id"));
    }

    @Test
    public void testCreateAsset() {
        given()
          .contentType("application/json")
          .body("{\"name\":\"Test Asset\",\"description\":\"Test Description\",\"category\":\"test\",\"value\":100.0}")
          .when().post("/api/v1/assets")
          .then()
             .statusCode(201)
             .body("message", is("Asset created successfully"));
    }

    @Test
    public void testUpdateAsset() {
        given()
          .contentType("application/json")
          .body("{\"name\":\"Updated Asset\",\"description\":\"Updated Description\",\"category\":\"test\",\"value\":200.0}")
          .when().put("/api/v1/assets/test-id")
          .then()
             .statusCode(200)
             .body("message", is("Asset updated successfully"));
    }

    @Test
    public void testDeleteAsset() {
        given()
          .when().delete("/api/v1/assets/test-id")
          .then()
             .statusCode(204);
    }

    @Test
    public void testHealthCheck() {
        given()
          .when().get("/api/v1/health")
          .then()
             .statusCode(200)
             .body("status", is("UP"))
             .body("service", is("asset-flow-api"));
    }

    @Test
    public void testReadinessCheck() {
        given()
          .when().get("/api/v1/health/ready")
          .then()
             .statusCode(200)
             .body("status", is("READY"));
    }

    @Test
    public void testLivenessCheck() {
        given()
          .when().get("/api/v1/health/live")
          .then()
             .statusCode(200)
             .body("status", is("ALIVE"));
    }
}
