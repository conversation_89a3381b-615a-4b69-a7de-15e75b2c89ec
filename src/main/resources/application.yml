# Quarkus Application Configuration
quarkus:
  application:
    name: asset-fow
    version: 1.0.0-SNAPSHOT
  
  # HTTP Configuration
  http:
    port: 8080
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      methods: "GET,PUT,POST,DELETE,OPTIONS"
      headers: "accept, authorization, content-type, x-requested-with"
  
  # Development Configuration
  dev:
    ui:
      always-include: true
  
  # Logging Configuration
  log:
    level: INFO
    category:
      "com.example.assetfow": DEBUG
    console:
      enable: true
      format: "%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n"
  
  # Health Check Configuration
  smallrye-health:
    ui:
      always-include: true
  
  # OpenAPI Configuration
  smallrye-openapi:
    info-title: Asset Flow Management API
    info-version: 1.0.0
    info-description: API for managing asset flows
    ui:
      always-include: true
  
  # Metrics Configuration
  micrometer:
    enabled: true
    export:
      prometheus:
        enabled: true

# Custom application properties
app:
  name: Asset Flow Management
  description: System for managing asset flows and transactions
